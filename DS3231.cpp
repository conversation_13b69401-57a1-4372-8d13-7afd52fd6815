#include "DS3231.h"
#include <Wire.h>
//#define CLOCK_ADDRESS 0x68//DS3231
#define CLOCK_ADDRESS 0x32//RX8025T

DS3231::DS3231(int sda, int sdc) 
{
    mySda = sda;
    mySdc = sdc;
}

void DS3231::init() {
  Wire.begin(mySda, mySdc);
  Wire.setClock(400000); 

  Wire.beginTransmission(CLOCK_ADDRESS);
  Wire.write(uint8_t(0x0E));
  Wire.write(uint8_t(0x00)); //00000011  UF TF AF =0 VLF=0 VDET=0
  Wire.endTransmission();

  Wire.beginTransmission(CLOCK_ADDRESS);
  Wire.write(uint8_t(0x0F));
  Wire.write(uint8_t(0x40));//01000000   CSEL1 UIE TIE AIE =0 CSEL0=1   2S temperature compensation & no INT 
  Wire.endTransmission();
}

/***************************************** 
	Public Functions
 *****************************************/

//void DS3231::getTime(byte& year, byte& month, byte& date, byte& DoW, byte& hour, byte& minute, byte& second) {
void DS3231::getTime(byte& year, char& month, char& date, char& DoW, char& hour, char& minute, char& second) {
  second = getSecond();
  minute = getMinute();
  hour = getHour();
  DoW = getDoW();
  date = getDate();
  month = getMonth();
  year = getYear();
}



byte DS3231::getSecond() {
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x00));
	Wire.endTransmission();

	Wire.requestFrom(CLOCK_ADDRESS, 1);
	return bcdToDec(Wire.read());
}

byte DS3231::getMinute() {
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(0x01);
	Wire.endTransmission();

	Wire.requestFrom(CLOCK_ADDRESS, 1);
	return bcdToDec(Wire.read());
}

byte DS3231::getHour() {
  Wire.beginTransmission(CLOCK_ADDRESS);
  Wire.write(0x02);
  Wire.endTransmission();

  Wire.requestFrom(CLOCK_ADDRESS, 1);
  return bcdToDec(Wire.read());
}

byte DS3231::getDoW() {
  unsigned char temp,gDoW;
  Wire.beginTransmission(CLOCK_ADDRESS);
  Wire.write(uint8_t(0x03));
  Wire.endTransmission();

  Wire.requestFrom(CLOCK_ADDRESS, 1);
  temp=Wire.read();
  switch (temp)
  {
  case 0x01:
  gDoW = 7;
  break; 
  case 0x02:
  gDoW = 1;
  break; 
  case 0x04:
  gDoW = 2;
  break; 
  case 0x08:
  gDoW = 3;
  break; 
  case 0x10:
  gDoW = 4;
  break; 
  case 0x20:
  gDoW = 5;
  break; 
  case 0x40:
  gDoW = 6;
  break;   
  }
  //return bcdToDec(Wire.read());
 return gDoW;
}

byte DS3231::getDate() {
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x04));
	Wire.endTransmission();

	Wire.requestFrom(CLOCK_ADDRESS, 1);
	return bcdToDec(Wire.read());
}

byte DS3231::getMonth() {
  Wire.beginTransmission(CLOCK_ADDRESS);
  Wire.write(uint8_t(0x05));
  Wire.endTransmission();

  Wire.requestFrom(CLOCK_ADDRESS, 1);
  return bcdToDec(Wire.read());
}

byte DS3231::getYear() {
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x06));
	Wire.endTransmission();

	Wire.requestFrom(CLOCK_ADDRESS, 1);
	return bcdToDec(Wire.read());
}

void DS3231::setSecond(byte Second) {
	// Sets the seconds 
	// This function also resets the Oscillator Stop Flag, which is set
	// whenever power is interrupted.
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x00));
	Wire.write(decToBcd(Second));	
	Wire.endTransmission();
	// Clear OSF flag
	//byte temp_buffer = readControlByte(1);
	//writeControlByte((temp_buffer & 0b01111111), 1);
}

void DS3231::setMinute(byte Minute) {
	// Sets the minutes 
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x01));
	Wire.write(decToBcd(Minute));	
	Wire.endTransmission();
}

void DS3231::setHour(byte Hour) {
  
  Wire.beginTransmission(CLOCK_ADDRESS);
  Wire.write(uint8_t(0x02));
  Wire.write(decToBcd(Hour));
  Wire.endTransmission();
}


void DS3231::setDoW(byte DoW) {
  // Sets the Day of Week
  unsigned char temp;
  Wire.beginTransmission(CLOCK_ADDRESS);
  Wire.write(uint8_t(0x03));
  switch (DoW)
  {
  case 7:
  temp = 0x01;
  break; 
  case 1:
  temp = 0x02;
  break; 
  case 2:
  temp = 0x04;
  break; 
  case 3:
  temp = 0x08;
  break; 
  case 4:
  temp = 0x10;
  break; 
  case 5:
  temp = 0x20;
  break; 
  case 6:
  temp = 0x40;
  break;   
  }
  Wire.write(temp);
  //Wire.write(decToBcd(DoW));  
  Wire.endTransmission();
}

void DS3231::setDate(byte Date) {
	// Sets the Date
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x04));
	Wire.write(decToBcd(Date));	
	Wire.endTransmission();
}

void DS3231::setMonth(byte Month) {
	// Sets the month
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x05));
	Wire.write(decToBcd(Month));	
	Wire.endTransmission();
}

void DS3231::setYear(byte Year) {
	// Sets the year
	Wire.beginTransmission(CLOCK_ADDRESS);
	Wire.write(uint8_t(0x06));
	Wire.write(decToBcd(Year));	
	Wire.endTransmission();
}


//void DS3231::setTime(byte year, byte month, byte date, byte DoW, byte hour, byte minute, byte second) {
void DS3231::setTime(char year, char month, char date, char DoW, char hour, char minute, char second) {
  setSecond(second);//Set the second 
  setMinute(minute);//Set the minute 
  setHour(hour);  //Set the hour 
  setDoW(DoW);    //Set the day of the week
  setDate(date);  //Set the date of the month
  setMonth(month);  //Set the month of the year
  setYear(year);  //Set the year (Last two digits of the year)

}



/***************************************** 
	Private Functions
 *****************************************/

byte DS3231::decToBcd(byte val) {
// Convert normal decimal numbers to binary coded decimal
	return ( (val/10*16) + (val%10) );
}

byte DS3231::bcdToDec(byte val) {
// Convert binary coded decimal to normal decimal numbers
	return ( (val/16*10) + (val%16) );
}


