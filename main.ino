#include <Wire.h>
#include "DS3231.h"
#include <OneButton.h>

//RTC
#define SDA_PIN 18//IIC_SDA
#define SCL_PIN 19//IIC_SCL
DS3231 Clock(SDA_PIN, SCL_PIN);
// VFD数据引脚
uint8_t clk   =3;//定义vfd clk数字接口14
uint8_t din   =5;//定义vfd data数字接口13
uint8_t cs    =8;//定义vfd cs数字接口15

//BUTTON
#define BUTTON_PIN1 9 //定义button数字接口0
#define BUTTON_PIN2 2//定义button数字接口16
#define key3   10      //定义button数字接口2

#define DOUBLECLICK_TIME 400
uint8_t keyen  =1;

//BEEP
#define BEEP 4//定义beep数字接口12

// Setup a new OneButton on pin PIN_INPUT1.
OneButton button1(BUTTON_PIN1, true, true);
// Setup a new OneButton on pin PIN_INPUT2.
OneButton button2(BUTTON_PIN2, true, true);

unsigned char liangcmd,ntpcount;
char year, month, date, DoW, hour, minute, second;
unsigned char mon,days,shi,fen,miao;
int years=1970;

//system time
int lightadc;
unsigned long perious =0; 
unsigned long current = 0;


void lightset();
void bee();


#define NOP do { __asm__ __volatile__ ("nop"); } while (0)
//////////////////////////////////////////////////////
void delay1()
{ 
  for(ulong j=0;j<1000;j++) NOP;
}

void write_6302(unsigned char w_data)
 { 
    unsigned char i;      
    for(i=0;i<8;i++)   
    {  
        digitalWrite(clk, LOW);   
        delay1(); 
      if( (w_data&0x01) == 0x01)       
      {digitalWrite(din, HIGH);}        
      else       
      {digitalWrite(din, LOW); } 
      w_data>>=1;  
      delay1();        
      digitalWrite(clk, HIGH);        
    }
 }  


//自定义字库显示函数
//x:0~7 位置  y:0-7 支持8个自定义字符,*s字库数组
void S1201_WriteUserFont(unsigned char x,unsigned char y, const unsigned char*s)
{
  unsigned char i=0;

  digitalWrite(cs, LOW); 
  delay1();
  write_6302(0x40+y);  
  for(i=0; i<5; i++)
  { write_6302(pgm_read_byte(& s[i]));}  
   digitalWrite(cs, HIGH);
   digitalWrite(cs, LOW);
   write_6302(0x20+x);
   write_6302(0x00+y);
   digitalWrite(cs, HIGH);
   //S1201_show();
}


void VFD_cmd(unsigned char command)
{
  digitalWrite(cs, LOW);
  delay1();
  write_6302(command);
  digitalWrite(cs, HIGH);
  delay1();
}


void S1201_show(void)
{
  digitalWrite(cs, LOW);//开始传输
  delay1();
  write_6302(0xe8);     //地址寄存器起始位置
  digitalWrite(cs, HIGH); //停止传输
}

void VFD_init()
{
  //SET HOW MANY digtal numbers
  digitalWrite(cs, LOW);
  delay1();
  write_6302(0xe0);
  delay1();
  write_6302(0x0f);//16 digtal 
  digitalWrite(cs, HIGH);
  delay1();

  //set bright
  digitalWrite(cs, LOW);
  delay1();
  write_6302(0xe4);
  delay1();
  write_6302(0xeF);//leve 255 max
  digitalWrite(cs, HIGH);
  delay1();
}

/******************************
  在指定位置打印一个字符(用户自定义,所有CG-ROM中的)
  x:0~11;chr:要显示的字符编码
*******************************/
void S1201_WriteOneChar(unsigned char x, unsigned char chr)
{
  digitalWrite(cs, LOW);  //开始传输
  delay1();
  write_6302(0x20 + x); //地址寄存器起始位置
  write_6302(chr + 0x30);
  digitalWrite(cs, HIGH); //停止传输
  //S1201_show();
}

/******************************
  在指定位置打印字符串
  (仅适用于英文,标点,数字)
  x:0~11;str:要显示的字符串
*******************************/
void S1201_WriteStr(unsigned char x, char *str)
{
  digitalWrite(cs, LOW);  //开始传输
  delay1();
  write_6302(0x20 + x); //地址寄存器起始位置
  while (*str)
  {
   //Serial.println(*str);
    write_6302(*str); //ascii与对应字符表转换
    str++;
  }
  digitalWrite(cs, HIGH); //停止传输
  //S1201_show();
}

void ReadDS3231()
{
  Clock.getTime((byte&)year,month,date,DoW,hour,minute,second); 
}
void set3231(){
  Clock.setTime((years-2000),mon,days,DoW,shi,fen,miao);
}
//显示函数(x坐标,data数据)
void displaydata()
{
    S1201_WriteStr(0,"ABCDEFG-"); 
    S1201_WriteOneChar(8,hour/10);
    S1201_WriteOneChar(9,hour%10);
    S1201_WriteStr(10,":");
    S1201_WriteOneChar(11,minute/10);
    S1201_WriteOneChar(12,minute%10);
    S1201_WriteStr(13,":");
    S1201_WriteOneChar(14,second/10);
    S1201_WriteOneChar(15,second%10);
  
    S1201_show();//开显示
  }



void lightset()
{
    //set bright
    digitalWrite(cs, LOW);
    write_6302(0xe4);
    write_6302(liangcmd);//level liangcmd 0~255
    digitalWrite(cs, HIGH);
 }
   
/******单击******/
void click1()
{
lightadc=analogRead(1);//read brightADC
S1201_WriteStr(0,"click1  adc:");
S1201_WriteOneChar(12,lightadc/1000%10);
S1201_WriteOneChar(13,lightadc/100%10);
S1201_WriteOneChar(14,lightadc/10%10);
S1201_WriteOneChar(15,lightadc%10);

S1201_show();//开显示
delay(1000);
}
/******双击*****/
void doubleclick1()
{
S1201_WriteStr(0,"doubleclick1    ");
S1201_show();//开显示
delay(1000);   
}

/******单击******/
void click2() 
{
S1201_WriteStr(0,"click2 test     ");
S1201_show();//开显示
delay(1000); 
}

/******双击*****/
void doubleclick2()
{
S1201_WriteStr(0,"doubleclick2    ");
S1201_show();//开显示
delay(1000);
}  


/******长按开始******/
void longPressStart2()
{
S1201_WriteStr(0,"longPressStart2 ");
S1201_show();//开显示
delay(1000);  
}

void key3set()
{
   if(digitalRead(key3)==LOW&&keyen==1)
   { 
    delay(20);
    if(digitalRead(key3)==LOW)
    {
     keyen=0;
     S1201_WriteStr(0,"click3 test     ");
     S1201_show();//开显示
     delay(1000); 
    }
   }
  if(digitalRead(key3)==HIGH&&keyen==0)
   keyen=1;
 }
 

void setup() {
  // put your setup code here, to run once:
  Serial.begin(115200);
  pinMode(clk, OUTPUT);
  pinMode(din, OUTPUT);
  pinMode(cs, OUTPUT);
  pinMode(SDA_PIN, INPUT_PULLUP);
  pinMode(SCL_PIN, INPUT_PULLUP);
  pinMode(key3, INPUT_PULLUP);
  pinMode(BEEP, OUTPUT);
  digitalWrite(BEEP, HIGH);
  

  //vfd
  VFD_init();
  
  //KEY
  button1.reset();//清除按钮状态机的状态
  button1.attachClick(click1);//注册单击
  button1.attachDoubleClick(doubleclick1);//注册双击
//  button1.attachLongPressStart(longPressStart1);//注册长按开始
  
  button2.reset();//清除按钮状态机的状态
  button2.attachClick(click2);//注册单击
  button2.attachDoubleClick(doubleclick2);//注册双击
  button2.attachLongPressStart(longPressStart2);//注册长按开始   
  
  Clock.init();//RTC 时钟芯片初始化
  ReadDS3231();
  lightset();      
}

void loop() {
    
   //system time
   current = millis();
   
   //buttom handle 
   button1.tick();
   button2.tick();
   key3set();
   //handle vfd flash// 60ms
   if(current-perious>60)
  { 
    ntpcount++;
    //RTC and display and alram
    ReadDS3231();
          
   //VFD auto bright
   liangcmd=150;
   lightset();
    
   displaydata();  
   perious=current;   
  }   
}
