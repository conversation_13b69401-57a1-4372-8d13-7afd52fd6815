
#ifndef DS3231_h
#define DS3231_h

#include <Arduino.h>
#include <Wire.h>

class DS3231 {
  
	public:
		DS3231(int sda, int sdc);
    void init();
//		void getTime(byte& year, byte& month, byte& date, byte& DoW, byte& hour, byte& minute, byte& second); 
    void getTime(byte& year, char& month, char& date, char& DoW, char& hour, char& minute, char& second);
    void setTime(byte& year, char& month, char& date, char& DoW, char& hour, char& minute, char& second);
		byte getSecond(); 
		byte getMinute(); 
		byte getHour(); 
			// In addition to returning the hour register, this function
			// returns the values of the 12/24-hour flag and the AM/PM flag.
		byte getDoW(); 
		byte getDate(); 
		byte getMonth(); 
		byte getYear(); 
		
		void setSecond(byte Second); 
		void setMinute(byte Minute); 
		void setHour(byte Hour); 
		void setDoW(byte DoW);  // Sets the Day of the Week (1-7);
		void setDate(byte Date); 
		void setMonth(byte Month); 
		void setYear(byte Year); 
		//void setClockMode(bool h12); // Set 12/24h mode. True is 12-h, false is 24-hour.
    //void setTime(byte year, byte month, byte date, byte DoW, byte hour, byte minute, byte second);
    void setTime(char year, char month, char date, char DoW, char hour, char minute, char second);    
		//float getTemperature(); 
		
		//void getA1Time(byte& A1Day, byte& A1Hour, byte& A1Minute, byte& A1Second, byte& AlarmBits, bool& A1Dy, bool& A1h12, bool& A1PM); 
/* Retrieves everything you could want to know about alarm
 * one. 
 * A1Dy true makes the alarm go on A1Day = Day of Week,
 * A1Dy false makes the alarm go on A1Day = Date of month.
 *
 * byte AlarmBits sets the behavior of the alarms:
 *	Dy	A1M4	A1M3	A1M2	A1M1	Rate
 *	X	1		1		1		1		Once per second
 *	X	1		1		1		0		Alarm when seconds match
 *	X	1		1		0		0		Alarm when min, sec match
 *	X	1		0		0		0		Alarm when hour, min, sec match
 *	0	0		0		0		0		Alarm when date, h, m, s match
 *	1	0		0		0		0		Alarm when DoW, h, m, s match
 *
 *	Dy	A2M4	A2M3	A2M2	Rate
 *	X	1		1		1		Once per minute (at seconds = 00)
 *	X	1		1		0		Alarm when minutes match
 *	X	1		0		0		Alarm when hours and minutes match
 *	0	0		0		0		Alarm when date, hour, min match
 *	1	0		0		0		Alarm when DoW, hour, min match
 */
		//void getA2Time(byte& A2Day, byte& A2Hour, byte& A2Minute, byte& AlarmBits, bool& A2Dy, bool& A2h12, bool& A2PM); 
		//void setA1Time(byte A1Day, byte A1Hour, byte A1Minute, byte A1Second, byte AlarmBits, bool A1Dy, bool A1h12, bool A1PM); 
		//void setA2Time(byte A2Day, byte A2Hour, byte A2Minute, byte AlarmBits, bool A2Dy, bool A2h12, bool A2PM); 
		//void turnOnAlarm(byte Alarm); 
		//void turnOffAlarm(byte Alarm); 
		//bool checkAlarmEnabled(byte Alarm); 
		//bool checkIfAlarm(byte Alarm); 


		//void enableOscillator(bool TF, bool battery, byte frequency); 
			// frequency must be 0, 1, 2, or 3.
			// 0 = 1 Hz
			// 1 = 1.024 kHz
			// 2 = 4.096 kHz
			// 3 = 8.192 kHz (Default if frequency byte is out of range);
		//void enable32kHz(bool TF); 
			// Turns the 32kHz output pin on (true); or off (false).
		//bool oscillatorCheck();;
      // Write the selected control byte. 
      // which == false -> 0x0e, true->0x0f.
    //void writeControlByte(byte control, bool which);
     
	private:

		   int myI2cAddress;
		   int mySda;
		   int mySdc;
		
		byte decToBcd(byte val); 
		byte bcdToDec(byte val); 
		byte readControlByte(bool which); // Read selected control byte: (0); reads 0x0e, (1) reads 0x0f
		

};

#endif
